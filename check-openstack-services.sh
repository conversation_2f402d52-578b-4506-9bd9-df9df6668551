#!/bin/bash
# 检查OpenStack在Kubernetes中的服务配置

echo "🔍 检查OpenStack服务配置..."

# 1. 查看所有OpenStack相关的Service
echo "=== OpenStack Services ==="
kubectl get svc -n openstack -o wide

echo ""
echo "=== Service详细信息 ==="

# 2. 检查关键API服务的Service配置
SERVICES=("keystone" "neutron" "nova" "trove" "glance" "cinder")

for service in "${SERVICES[@]}"; do
    echo "--- $service 相关服务 ---"
    kubectl get svc -n openstack | grep -i $service || echo "未找到 $service 服务"
    echo ""
done

# 3. 查看Service的Endpoint
echo "=== Service Endpoints ==="
kubectl get endpoints -n openstack | head -20

# 4. 检查API Pod的标签
echo ""
echo "=== API Pod标签信息 ==="
echo "Neutron Server Pods:"
kubectl get pods -n openstack -l application=neutron,component=server -o wide 2>/dev/null || \
kubectl get pods -n openstack | grep neutron-server

echo ""
echo "Nova API Pods:"
kubectl get pods -n openstack -l application=nova,component=api -o wide 2>/dev/null || \
kubectl get pods -n openstack | grep nova-api

echo ""
echo "Keystone Pods:"
kubectl get pods -n openstack -l application=keystone -o wide 2>/dev/null || \
kubectl get pods -n openstack | grep keystone

# 5. 检查是否有Ingress配置
echo ""
echo "=== Ingress配置 ==="
kubectl get ingress -n openstack 2>/dev/null || echo "未找到Ingress配置"

# 6. 检查NodePort服务
echo ""
echo "=== NodePort服务 ==="
kubectl get svc -n openstack --field-selector spec.type=NodePort 2>/dev/null || echo "未找到NodePort服务"

# 7. 生成访问建议
echo ""
echo "🎯 访问建议："
echo "1. 集群内访问（推荐）："
echo "   - Keystone: http://keystone-api.openstack.svc.cluster.local:5000"
echo "   - Neutron: http://neutron-server.openstack.svc.cluster.local:9696"
echo "   - Nova: http://nova-api-osapi.openstack.svc.cluster.local:8774"
echo ""
echo "2. 如需外部访问，建议创建NodePort服务"
echo ""
echo "3. 检查现有配置："
echo "   kubectl describe svc <service-name> -n openstack"
