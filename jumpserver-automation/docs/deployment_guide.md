# JumpServer多云池自动化部署指南

## 环境要求

### 系统要求
- Python 3.8+
- Linux/macOS/Windows
- 网络连接到JumpServer和各云平台

### JumpServer要求
- JumpServer v2.x 或 v3.x
- 管理员权限或API访问权限
- 已配置Access Key或用户凭据

## 安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd jumpserver-automation
```

### 2. 创建虚拟环境
```bash
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 配置文件设置

#### 4.1 配置JumpServer连接
编辑 `config/jumpserver_config.yaml`:

```yaml
jumpserver:
  base_url: "https://your-jumpserver.com"
  auth:
    access_key_id: "your-access-key-id"
    access_key_secret: "your-access-key-secret"
```

#### 4.2 配置云池信息
编辑 `config/cloud_pools.yaml`:

```yaml
cloud_pools:
  openstack_prod:
    name: "OpenStack生产环境"
    type: "openstack"
    connection:
      auth_url: "http://keystone.example.com:5000/v3"
      username: "admin"
      password: "your-password"
      project_name: "admin"
      domain_name: "default"
```

## 使用方法

### 基础使用
```bash
# 运行基础示例
python examples/basic_usage.py

# 同步所有云池资产
python scripts/sync_assets.py

# 同步指定云池
python scripts/sync_assets.py --pool openstack_prod

# 生成同步报告
python scripts/sync_assets.py --output sync_report.json
```

### 批量操作
```bash
# 批量用户操作
python scripts/batch_user_ops.py --action create --file users.csv

# 权限审计
python scripts/permission_audit.py --output audit_report.xlsx

# 会话报告
python scripts/session_report.py --start-date 2024-01-01 --end-date 2024-01-31
```

## 配置说明

### JumpServer认证方式

#### 方式1: Access Key (推荐)
```yaml
auth:
  access_key_id: "your-access-key-id"
  access_key_secret: "your-access-key-secret"
```

#### 方式2: 用户名密码
```yaml
auth:
  username: "admin"
  password: "your-password"
```

#### 方式3: Token
```yaml
auth:
  token: "your-api-token"
```

### 云池类型配置

#### OpenStack
```yaml
openstack_pool:
  type: "openstack"
  connection:
    auth_url: "http://keystone:5000/v3"
    username: "admin"
    password: "password"
    project_name: "admin"
    domain_name: "default"
```

#### VMware vSphere
```yaml
vmware_pool:
  type: "vmware"
  connection:
    host: "vcenter.example.com"
    username: "<EMAIL>"
    password: "password"
    datacenter: "Datacenter1"
```

#### 阿里云
```yaml
aliyun_pool:
  type: "aliyun"
  connection:
    access_key_id: "your-aliyun-key"
    access_key_secret: "your-aliyun-secret"
    region_id: "cn-hangzhou"
```

## 定时任务设置

### 使用Cron (Linux/macOS)
```bash
# 编辑crontab
crontab -e

# 每小时同步一次资产
0 * * * * /path/to/venv/bin/python /path/to/jumpserver-automation/scripts/sync_assets.py

# 每天生成会话报告
0 2 * * * /path/to/venv/bin/python /path/to/jumpserver-automation/scripts/session_report.py
```

### 使用systemd定时器 (Linux)
创建服务文件 `/etc/systemd/system/jumpserver-sync.service`:
```ini
[Unit]
Description=JumpServer Asset Sync
After=network.target

[Service]
Type=oneshot
User=jumpserver
WorkingDirectory=/opt/jumpserver-automation
ExecStart=/opt/jumpserver-automation/venv/bin/python scripts/sync_assets.py
```

创建定时器文件 `/etc/systemd/system/jumpserver-sync.timer`:
```ini
[Unit]
Description=Run JumpServer Asset Sync hourly
Requires=jumpserver-sync.service

[Timer]
OnCalendar=hourly
Persistent=true

[Install]
WantedBy=timers.target
```

启用定时器:
```bash
sudo systemctl enable jumpserver-sync.timer
sudo systemctl start jumpserver-sync.timer
```

## 监控和日志

### 日志配置
日志文件位置: `logs/jumpserver_automation.log`

调整日志级别:
```yaml
logging:
  level: "DEBUG"  # DEBUG, INFO, WARNING, ERROR
```

### 监控指标
如果启用了监控，可以访问: `http://localhost:8080/metrics`

### 健康检查
```bash
# 检查JumpServer连接
python -c "
from src.jumpserver_client import JumpServerClient
import yaml
with open('config/jumpserver_config.yaml') as f:
    config = yaml.safe_load(f)
client = JumpServerClient(config['jumpserver'])
print('健康状态:', client.health_check())
"
```

## 故障排除

### 常见问题

#### 1. 认证失败
- 检查Access Key是否正确
- 确认JumpServer版本兼容性
- 验证网络连接

#### 2. 资产同步失败
- 检查云平台凭据
- 确认网络连通性
- 查看详细错误日志

#### 3. 权限不足
- 确认JumpServer用户权限
- 检查API访问权限
- 验证组织权限

### 调试模式
```bash
# 启用调试日志
export PYTHONPATH=/path/to/jumpserver-automation/src
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
# 运行你的代码
"
```

## 安全建议

1. **凭据管理**
   - 使用环境变量存储敏感信息
   - 定期轮换Access Key
   - 限制API权限范围

2. **网络安全**
   - 使用HTTPS连接
   - 配置防火墙规则
   - 启用SSL证书验证

3. **访问控制**
   - 最小权限原则
   - 定期审计权限
   - 监控异常活动

## 扩展开发

### 添加新的云平台支持
1. 在 `src/cloud_providers/` 创建新的提供商类
2. 实现 `discover_assets()` 方法
3. 在 `cloud_pool_manager.py` 中注册新类型
4. 更新配置文件模板

### 自定义同步逻辑
1. 继承 `AssetManager` 类
2. 重写 `_convert_cloud_asset()` 方法
3. 添加自定义字段映射

## 支持和贡献

- 问题反馈: [GitHub Issues]
- 功能请求: [GitHub Discussions]
- 贡献代码: [Pull Requests]

## 许可证

MIT License - 详见 LICENSE 文件
