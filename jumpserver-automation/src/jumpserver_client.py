#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JumpServer API客户端
提供与JumpServer API交互的基础功能
"""

import requests
import json
import time
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class JumpServerAPIError(Exception):
    """JumpServer API异常"""
    pass


class JumpServerClient:
    """JumpServer API客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化JumpServer客户端
        
        Args:
            config: 配置字典
        """
        self.base_url = config['base_url'].rstrip('/')
        self.api_version = config.get('api_version', 'v1')
        self.timeout = config.get('connection', {}).get('timeout', 30)
        self.retry_times = config.get('connection', {}).get('retry_times', 3)
        self.retry_delay = config.get('connection', {}).get('retry_delay', 1)
        self.verify_ssl = config.get('connection', {}).get('verify_ssl', True)
        
        # 认证信息
        self.auth_config = config['auth']
        self.session = requests.Session()
        self.session.verify = self.verify_ssl
        
        # 认证
        self._authenticate()
        
    def _authenticate(self):
        """认证到JumpServer"""
        auth_config = self.auth_config
        
        if 'access_key_id' in auth_config and 'access_key_secret' in auth_config:
            # 使用Access Key认证
            self._auth_with_access_key()
        elif 'username' in auth_config and 'password' in auth_config:
            # 使用用户名密码认证
            self._auth_with_password()
        elif 'token' in auth_config:
            # 使用Token认证
            self._auth_with_token()
        else:
            raise JumpServerAPIError("未提供有效的认证信息")
            
    def _auth_with_access_key(self):
        """使用Access Key认证"""
        access_key_id = self.auth_config['access_key_id']
        access_key_secret = self.auth_config['access_key_secret']
        
        # 设置认证头
        self.session.headers.update({
            'Authorization': f'Token {access_key_id}:{access_key_secret}',
            'Content-Type': 'application/json',
            'X-JMS-ORG': '00000000-0000-0000-0000-000000000002'  # 默认组织
        })
        
        # 验证认证
        try:
            response = self.get('/api/v1/users/profile/')
            logger.info(f"认证成功，当前用户: {response.get('username')}")
        except Exception as e:
            raise JumpServerAPIError(f"Access Key认证失败: {e}")
            
    def _auth_with_password(self):
        """使用用户名密码认证"""
        username = self.auth_config['username']
        password = self.auth_config['password']
        
        # 获取Token
        auth_url = urljoin(self.base_url, '/api/v1/authentication/auth/')
        auth_data = {
            'username': username,
            'password': password
        }
        
        try:
            response = self.session.post(auth_url, json=auth_data, timeout=self.timeout)
            response.raise_for_status()
            
            token = response.json().get('token')
            if not token:
                raise JumpServerAPIError("未获取到认证Token")
                
            # 设置认证头
            self.session.headers.update({
                'Authorization': f'Token {token}',
                'Content-Type': 'application/json',
                'X-JMS-ORG': '00000000-0000-0000-0000-000000000002'
            })
            
            logger.info(f"用户名密码认证成功: {username}")
            
        except requests.RequestException as e:
            raise JumpServerAPIError(f"用户名密码认证失败: {e}")
            
    def _auth_with_token(self):
        """使用Token认证"""
        token = self.auth_config['token']
        
        self.session.headers.update({
            'Authorization': f'Token {token}',
            'Content-Type': 'application/json',
            'X-JMS-ORG': '00000000-0000-0000-0000-000000000002'
        })
        
        # 验证Token
        try:
            response = self.get('/api/v1/users/profile/')
            logger.info(f"Token认证成功，当前用户: {response.get('username')}")
        except Exception as e:
            raise JumpServerAPIError(f"Token认证失败: {e}")
            
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 请求参数
            
        Returns:
            响应数据
        """
        url = urljoin(self.base_url, endpoint)
        
        for attempt in range(self.retry_times):
            try:
                response = self.session.request(
                    method=method,
                    url=url,
                    timeout=self.timeout,
                    **kwargs
                )
                
                # 检查HTTP状态码
                if response.status_code == 401:
                    raise JumpServerAPIError("认证失败，请检查认证信息")
                elif response.status_code == 403:
                    raise JumpServerAPIError("权限不足")
                elif response.status_code == 404:
                    raise JumpServerAPIError(f"资源不存在: {endpoint}")
                elif response.status_code >= 400:
                    raise JumpServerAPIError(f"请求失败: {response.status_code} - {response.text}")
                    
                response.raise_for_status()
                
                # 解析JSON响应
                if response.content:
                    return response.json()
                else:
                    return {}
                    
            except requests.RequestException as e:
                if attempt == self.retry_times - 1:
                    raise JumpServerAPIError(f"请求失败: {e}")
                    
                logger.warning(f"请求失败，重试 {attempt + 1}/{self.retry_times}: {e}")
                time.sleep(self.retry_delay)
                
        raise JumpServerAPIError("请求重试次数超限")
        
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """GET请求"""
        return self._make_request('GET', endpoint, params=params)
        
    def post(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """POST请求"""
        return self._make_request('POST', endpoint, json=data)
        
    def put(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """PUT请求"""
        return self._make_request('PUT', endpoint, json=data)
        
    def patch(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """PATCH请求"""
        return self._make_request('PATCH', endpoint, json=data)
        
    def delete(self, endpoint: str) -> Dict[str, Any]:
        """DELETE请求"""
        return self._make_request('DELETE', endpoint)
        
    def get_paginated(self, endpoint: str, params: Optional[Dict] = None, 
                     page_size: int = 100, max_pages: int = 50) -> List[Dict[str, Any]]:
        """
        获取分页数据
        
        Args:
            endpoint: API端点
            params: 查询参数
            page_size: 每页大小
            max_pages: 最大页数
            
        Returns:
            所有数据列表
        """
        all_data = []
        params = params or {}
        params['limit'] = page_size
        params['offset'] = 0
        
        for page in range(max_pages):
            response = self.get(endpoint, params)
            
            # 处理不同的分页响应格式
            if 'results' in response:
                data = response['results']
                total = response.get('count', 0)
            elif isinstance(response, list):
                data = response
                total = len(data)
            else:
                data = [response]
                total = 1
                
            all_data.extend(data)
            
            # 检查是否还有更多数据
            if len(data) < page_size or len(all_data) >= total:
                break
                
            params['offset'] += page_size
            
        logger.info(f"获取分页数据完成: {endpoint}, 总数: {len(all_data)}")
        return all_data
        
    def health_check(self) -> bool:
        """健康检查"""
        try:
            response = self.get('/api/v1/users/profile/')
            return bool(response.get('id'))
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return False
