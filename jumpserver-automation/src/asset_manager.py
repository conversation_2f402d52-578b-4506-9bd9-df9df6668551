#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资产管理模块
负责JumpServer资产的创建、更新、删除和同步
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from .jumpserver_client import JumpServerClient, JumpServerAPIError

logger = logging.getLogger(__name__)


class AssetManager:
    """资产管理器"""
    
    def __init__(self, client: JumpServerClient):
        """
        初始化资产管理器
        
        Args:
            client: JumpServer客户端
        """
        self.client = client
        
    def get_assets(self, filters: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        获取资产列表
        
        Args:
            filters: 过滤条件
            
        Returns:
            资产列表
        """
        try:
            params = filters or {}
            assets = self.client.get_paginated('/api/v1/assets/assets/', params)
            logger.info(f"获取资产列表成功，共 {len(assets)} 个资产")
            return assets
        except Exception as e:
            logger.error(f"获取资产列表失败: {e}")
            raise
            
    def get_asset_by_id(self, asset_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取资产
        
        Args:
            asset_id: 资产ID
            
        Returns:
            资产信息
        """
        try:
            asset = self.client.get(f'/api/v1/assets/assets/{asset_id}/')
            logger.info(f"获取资产成功: {asset.get('name')}")
            return asset
        except JumpServerAPIError as e:
            if "404" in str(e):
                logger.warning(f"资产不存在: {asset_id}")
                return None
            raise
            
    def create_asset(self, asset_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建资产
        
        Args:
            asset_data: 资产数据
            
        Returns:
            创建的资产信息
        """
        try:
            # 验证必需字段
            required_fields = ['name', 'address', 'platform']
            for field in required_fields:
                if field not in asset_data:
                    raise ValueError(f"缺少必需字段: {field}")
                    
            # 设置默认值
            asset_data.setdefault('is_active', True)
            asset_data.setdefault('protocols', self._get_default_protocols(asset_data.get('platform')))
            
            asset = self.client.post('/api/v1/assets/assets/', asset_data)
            logger.info(f"创建资产成功: {asset.get('name')} ({asset.get('id')})")
            return asset
        except Exception as e:
            logger.error(f"创建资产失败: {e}")
            raise
            
    def update_asset(self, asset_id: str, asset_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新资产
        
        Args:
            asset_id: 资产ID
            asset_data: 更新的资产数据
            
        Returns:
            更新后的资产信息
        """
        try:
            asset = self.client.patch(f'/api/v1/assets/assets/{asset_id}/', asset_data)
            logger.info(f"更新资产成功: {asset.get('name')} ({asset_id})")
            return asset
        except Exception as e:
            logger.error(f"更新资产失败: {e}")
            raise
            
    def delete_asset(self, asset_id: str) -> bool:
        """
        删除资产
        
        Args:
            asset_id: 资产ID
            
        Returns:
            是否删除成功
        """
        try:
            self.client.delete(f'/api/v1/assets/assets/{asset_id}/')
            logger.info(f"删除资产成功: {asset_id}")
            return True
        except Exception as e:
            logger.error(f"删除资产失败: {e}")
            return False
            
    def batch_create_assets(self, assets_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量创建资产
        
        Args:
            assets_data: 资产数据列表
            
        Returns:
            创建结果列表
        """
        results = []
        success_count = 0
        
        for asset_data in assets_data:
            try:
                asset = self.create_asset(asset_data)
                results.append({
                    'status': 'success',
                    'asset': asset,
                    'name': asset_data.get('name')
                })
                success_count += 1
            except Exception as e:
                results.append({
                    'status': 'failed',
                    'error': str(e),
                    'name': asset_data.get('name')
                })
                
        logger.info(f"批量创建资产完成: 成功 {success_count}/{len(assets_data)}")
        return results
        
    def sync_assets_from_cloud(self, cloud_assets: List[Dict[str, Any]], 
                              node_id: str, domain_id: str) -> Dict[str, Any]:
        """
        从云平台同步资产
        
        Args:
            cloud_assets: 云平台资产列表
            node_id: 节点ID
            domain_id: 域ID
            
        Returns:
            同步结果
        """
        sync_result = {
            'total': len(cloud_assets),
            'created': 0,
            'updated': 0,
            'skipped': 0,
            'failed': 0,
            'details': []
        }
        
        # 获取现有资产
        existing_assets = self.get_assets()
        existing_assets_map = {asset['address']: asset for asset in existing_assets}
        
        for cloud_asset in cloud_assets:
            try:
                asset_address = cloud_asset.get('address') or cloud_asset.get('ip')
                if not asset_address:
                    sync_result['failed'] += 1
                    sync_result['details'].append({
                        'name': cloud_asset.get('name'),
                        'status': 'failed',
                        'reason': '缺少IP地址'
                    })
                    continue
                    
                # 转换云资产数据为JumpServer格式
                jumpserver_asset = self._convert_cloud_asset(cloud_asset, node_id, domain_id)
                
                if asset_address in existing_assets_map:
                    # 更新现有资产
                    existing_asset = existing_assets_map[asset_address]
                    if self._should_update_asset(existing_asset, jumpserver_asset):
                        self.update_asset(existing_asset['id'], jumpserver_asset)
                        sync_result['updated'] += 1
                        sync_result['details'].append({
                            'name': jumpserver_asset['name'],
                            'status': 'updated',
                            'id': existing_asset['id']
                        })
                    else:
                        sync_result['skipped'] += 1
                        sync_result['details'].append({
                            'name': jumpserver_asset['name'],
                            'status': 'skipped',
                            'reason': '无需更新'
                        })
                else:
                    # 创建新资产
                    new_asset = self.create_asset(jumpserver_asset)
                    sync_result['created'] += 1
                    sync_result['details'].append({
                        'name': jumpserver_asset['name'],
                        'status': 'created',
                        'id': new_asset['id']
                    })
                    
            except Exception as e:
                sync_result['failed'] += 1
                sync_result['details'].append({
                    'name': cloud_asset.get('name'),
                    'status': 'failed',
                    'reason': str(e)
                })
                logger.error(f"同步资产失败: {cloud_asset.get('name')} - {e}")
                
        logger.info(f"资产同步完成: 创建 {sync_result['created']}, "
                   f"更新 {sync_result['updated']}, "
                   f"跳过 {sync_result['skipped']}, "
                   f"失败 {sync_result['failed']}")
        
        return sync_result
        
    def _convert_cloud_asset(self, cloud_asset: Dict[str, Any], 
                           node_id: str, domain_id: str) -> Dict[str, Any]:
        """
        转换云资产数据为JumpServer格式
        
        Args:
            cloud_asset: 云资产数据
            node_id: 节点ID
            domain_id: 域ID
            
        Returns:
            JumpServer资产数据
        """
        # 检测操作系统类型
        os_type = cloud_asset.get('os_type', '').lower()
        if 'windows' in os_type:
            platform = 'Windows'
            protocols = [{'name': 'rdp', 'port': 3389}]
        else:
            platform = 'Linux'
            protocols = [{'name': 'ssh', 'port': 22}]
            
        asset_data = {
            'name': cloud_asset.get('name'),
            'address': cloud_asset.get('address') or cloud_asset.get('ip'),
            'platform': platform,
            'protocols': protocols,
            'is_active': cloud_asset.get('status', '').lower() == 'active',
            'nodes': [node_id],
            'domain': domain_id,
            'comment': f"自动同步于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        }
        
        # 添加标签
        tags = cloud_asset.get('tags', [])
        tags.extend(['auto-sync', 'cloud-asset'])
        asset_data['labels'] = [{'name': tag, 'value': 'true'} for tag in tags]
        
        return asset_data
        
    def _should_update_asset(self, existing_asset: Dict[str, Any], 
                           new_asset_data: Dict[str, Any]) -> bool:
        """
        判断是否需要更新资产
        
        Args:
            existing_asset: 现有资产
            new_asset_data: 新资产数据
            
        Returns:
            是否需要更新
        """
        # 比较关键字段
        compare_fields = ['name', 'platform', 'is_active']
        
        for field in compare_fields:
            if existing_asset.get(field) != new_asset_data.get(field):
                return True
                
        return False
        
    def _get_default_protocols(self, platform: str) -> List[Dict[str, Any]]:
        """
        获取平台默认协议
        
        Args:
            platform: 平台类型
            
        Returns:
            协议列表
        """
        if platform.lower() == 'windows':
            return [{'name': 'rdp', 'port': 3389}]
        else:
            return [{'name': 'ssh', 'port': 22}]
