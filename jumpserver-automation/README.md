# JumpServer多云池自动化管理

## 项目概述

基于JumpServer API实现多个云池的自动化管理，包括资产管理、用户权限分配、会话监控等功能。

## 目录结构

```
jumpserver-automation/
├── README.md                    # 项目说明
├── requirements.txt             # Python依赖
├── config/                      # 配置文件
│   ├── jumpserver_config.yaml   # JumpServer连接配置
│   ├── cloud_pools.yaml         # 云池配置
│   └── logging.conf             # 日志配置
├── src/                         # 源代码
│   ├── __init__.py
│   ├── jumpserver_client.py     # JumpServer API客户端
│   ├── cloud_pool_manager.py    # 云池管理器
│   ├── asset_manager.py         # 资产管理
│   ├── user_manager.py          # 用户管理
│   ├── permission_manager.py    # 权限管理
│   └── session_monitor.py       # 会话监控
├── scripts/                     # 自动化脚本
│   ├── sync_assets.py           # 同步资产
│   ├── batch_user_ops.py        # 批量用户操作
│   ├── permission_audit.py      # 权限审计
│   └── session_report.py        # 会话报告
├── templates/                   # 模板文件
│   ├── asset_template.yaml      # 资产模板
│   ├── user_template.yaml       # 用户模板
│   └── permission_template.yaml # 权限模板
├── tests/                       # 测试文件
│   ├── test_jumpserver_client.py
│   ├── test_asset_manager.py
│   └── test_user_manager.py
├── docs/                        # 文档
│   ├── api_reference.md         # API参考
│   ├── deployment_guide.md      # 部署指南
│   └── user_guide.md            # 使用指南
└── examples/                    # 示例
    ├── basic_usage.py           # 基础使用示例
    ├── bulk_operations.py       # 批量操作示例
    └── monitoring_example.py    # 监控示例
```

## 主要功能

### 1. 资产管理
- 自动发现和同步云池资产
- 批量导入/导出资产信息
- 资产分组和标签管理
- 资产状态监控

### 2. 用户管理
- 批量用户创建和管理
- 用户组织架构同步
- 用户权限分配
- 用户活动监控

### 3. 权限管理
- 基于云池的权限分配
- 细粒度权限控制
- 权限审计和报告
- 临时权限管理

### 4. 会话监控
- 实时会话监控
- 会话录像管理
- 异常行为检测
- 会话统计报告

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置JumpServer连接
编辑 `config/jumpserver_config.yaml`

### 3. 配置云池信息
编辑 `config/cloud_pools.yaml`

### 4. 运行示例
```bash
python examples/basic_usage.py
```

## 支持的云池类型

- OpenStack云池
- VMware vSphere
- 阿里云ECS
- 腾讯云CVM
- AWS EC2
- 物理服务器

## API版本支持

- JumpServer v2.x
- JumpServer v3.x

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License
