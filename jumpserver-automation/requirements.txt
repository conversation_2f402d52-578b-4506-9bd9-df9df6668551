# JumpServer API客户端依赖
requests>=2.28.0
urllib3>=1.26.0

# 配置文件处理
PyYAML>=6.0
configparser>=5.3.0

# 日志处理
loguru>=0.6.0

# 数据处理
pandas>=1.5.0
openpyxl>=3.0.10

# 时间处理
python-dateutil>=2.8.2
pytz>=2022.1

# 加密和安全
cryptography>=3.4.8
paramiko>=2.11.0

# 异步支持
aiohttp>=3.8.0
asyncio-mqtt>=0.11.0

# 命令行工具
click>=8.1.0
rich>=12.5.0

# 测试框架
pytest>=7.1.0
pytest-asyncio>=0.19.0
pytest-mock>=3.8.0

# 代码质量
black>=22.6.0
flake8>=5.0.0
mypy>=0.971

# 文档生成
sphinx>=5.1.0
sphinx-rtd-theme>=1.0.0

# 监控和指标
prometheus-client>=0.14.0

# 数据库支持（可选）
SQLAlchemy>=1.4.0
pymongo>=4.2.0

# 云服务SDK（可选）
aliyun-python-sdk-ecs>=4.24.0
tencentcloud-sdk-python>=3.0.0
boto3>=1.24.0
pyvmomi>=7.0.3
