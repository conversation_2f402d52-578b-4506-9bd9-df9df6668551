# 云池配置文件
cloud_pools:
  # OpenStack云池配置
  openstack_pool_1:
    name: "OpenStack生产环境"
    type: "openstack"
    description: "生产环境OpenStack云池"
    
    # 连接信息
    connection:
      auth_url: "http://keystone.example.com:5000/v3"
      username: "admin"
      password: "admin_password"
      project_name: "admin"
      domain_name: "default"
      region_name: "RegionOne"
    
    # 网络配置
    networks:
      management: "***********/24"
      business: "************/24"
      
    # 资产发现配置
    discovery:
      enabled: true
      interval: 3600  # 发现间隔(秒)
      auto_import: true
      filters:
        - status: "ACTIVE"
        - flavor: "!m1.nano"
    
    # JumpServer配置
    jumpserver:
      node_name: "OpenStack-Prod"
      domain_name: "OpenStack生产域"
      admin_user_group: "openstack-admins"
      
  # VMware云池配置  
  vmware_pool_1:
    name: "VMware测试环境"
    type: "vmware"
    description: "VMware vSphere测试环境"
    
    connection:
      host: "vcenter.example.com"
      username: "<EMAIL>"
      password: "vmware_password"
      port: 443
      datacenter: "Datacenter1"
      
    discovery:
      enabled: true
      interval: 7200
      auto_import: false
      filters:
        - power_state: "poweredOn"
        - guest_os: "linux*"
        
    jumpserver:
      node_name: "VMware-Test"
      domain_name: "VMware测试域"
      admin_user_group: "vmware-admins"

  # 阿里云配置
  aliyun_pool_1:
    name: "阿里云华东区"
    type: "aliyun"
    description: "阿里云华东1区域"
    
    connection:
      access_key_id: "your-aliyun-access-key"
      access_key_secret: "your-aliyun-secret"
      region_id: "cn-hangzhou"
      
    discovery:
      enabled: true
      interval: 1800
      auto_import: true
      filters:
        - status: "Running"
        - instance_type: "ecs.*"
        
    jumpserver:
      node_name: "Aliyun-East"
      domain_name: "阿里云域"
      admin_user_group: "aliyun-admins"

  # 物理服务器池
  physical_pool_1:
    name: "物理服务器池"
    type: "physical"
    description: "数据中心物理服务器"
    
    # 静态资产列表
    assets:
      - name: "DB-Server-01"
        ip: "*************"
        hostname: "db01.example.com"
        os: "CentOS 7"
        tags: ["database", "mysql"]
        
      - name: "Web-Server-01"
        ip: "*************"
        hostname: "web01.example.com"
        os: "Ubuntu 20.04"
        tags: ["web", "nginx"]
        
    # 网络扫描配置
    network_scan:
      enabled: true
      subnets:
        - "***********/24"
        - "********/24"
      ports: [22, 3389]
      
    jumpserver:
      node_name: "Physical-Servers"
      domain_name: "物理服务器域"
      admin_user_group: "physical-admins"

# 全局配置
global_settings:
  # 默认标签
  default_tags:
    - "auto-managed"
    - "jumpserver-automation"
    
  # 默认协议
  default_protocols:
    linux: ["ssh"]
    windows: ["rdp"]
    
  # 用户组映射
  user_group_mapping:
    admin: "系统管理员"
    operator: "运维人员"
    developer: "开发人员"
    auditor: "审计人员"
    
  # 权限模板
  permission_templates:
    full_access:
      name: "完全访问"
      actions: ["connect", "upload", "download"]
      
    read_only:
      name: "只读访问"
      actions: ["connect"]
      
    operator_access:
      name: "运维访问"
      actions: ["connect", "upload"]

# 同步配置
sync_settings:
  # 同步间隔
  intervals:
    asset_sync: 3600      # 资产同步间隔(秒)
    user_sync: 1800       # 用户同步间隔(秒)
    permission_sync: 900  # 权限同步间隔(秒)
    
  # 冲突处理
  conflict_resolution:
    asset_conflict: "jumpserver_wins"  # jumpserver_wins, cloud_wins, manual
    user_conflict: "manual"
    
  # 删除策略
  deletion_policy:
    orphaned_assets: "disable"  # disable, delete, ignore
    inactive_users: "disable"
