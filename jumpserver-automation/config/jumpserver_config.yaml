# JumpServer连接配置
jumpserver:
  # JumpServer服务器地址
  base_url: "https://jumpserver.example.com"
  
  # API认证信息
  auth:
    # 方式1: 使用Access Key (推荐)
    access_key_id: "your-access-key-id"
    access_key_secret: "your-access-key-secret"
    
    # 方式2: 使用用户名密码
    # username: "admin"
    # password: "your-password"
    
    # 方式3: 使用Token
    # token: "your-api-token"
  
  # API版本
  api_version: "v1"
  
  # 连接配置
  connection:
    timeout: 30
    retry_times: 3
    retry_delay: 1
    verify_ssl: true
    
  # 分页配置
  pagination:
    page_size: 100
    max_pages: 50

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/jumpserver_automation.log"
  max_size: "10MB"
  backup_count: 5

# 缓存配置
cache:
  enabled: true
  type: "memory"  # memory, redis, file
  ttl: 300  # 缓存时间(秒)
  
  # Redis配置(当type为redis时)
  redis:
    host: "localhost"
    port: 6379
    db: 0
    password: ""

# 监控配置
monitoring:
  enabled: true
  metrics_port: 8080
  health_check_interval: 60

# 安全配置
security:
  # 敏感信息加密
  encrypt_secrets: true
  encryption_key: "your-encryption-key"
  
  # API限流
  rate_limit:
    enabled: true
    requests_per_minute: 100
    
# 并发配置
concurrency:
  max_workers: 10
  batch_size: 50
  async_enabled: true
