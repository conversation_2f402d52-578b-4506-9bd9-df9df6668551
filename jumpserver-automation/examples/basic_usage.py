#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JumpServer自动化基础使用示例
演示如何使用JumpServer API进行基本操作
"""

import sys
import os
import yaml
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from jumpserver_client import JumpServerClient
from asset_manager import AssetManager
from user_manager import UserManager
from permission_manager import PermissionManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_config():
    """加载配置文件"""
    config_dir = Path(__file__).parent.parent / 'config'
    
    # 加载JumpServer配置
    with open(config_dir / 'jumpserver_config.yaml', 'r', encoding='utf-8') as f:
        js_config = yaml.safe_load(f)
    
    # 加载云池配置
    with open(config_dir / 'cloud_pools.yaml', 'r', encoding='utf-8') as f:
        cloud_config = yaml.safe_load(f)
    
    return js_config, cloud_config


def demo_asset_operations(asset_manager: AssetManager):
    """演示资产操作"""
    logger.info("=== 资产管理演示 ===")
    
    # 1. 获取现有资产
    logger.info("1. 获取现有资产列表")
    assets = asset_manager.get_assets()
    logger.info(f"当前共有 {len(assets)} 个资产")
    
    # 2. 创建测试资产
    logger.info("2. 创建测试资产")
    test_asset = {
        'name': 'test-server-001',
        'address': '*************',
        'platform': 'Linux',
        'protocols': [{'name': 'ssh', 'port': 22}],
        'comment': '自动化测试资产'
    }
    
    try:
        new_asset = asset_manager.create_asset(test_asset)
        logger.info(f"创建资产成功: {new_asset['name']} (ID: {new_asset['id']})")
        
        # 3. 更新资产
        logger.info("3. 更新资产信息")
        update_data = {
            'comment': '更新后的测试资产',
            'is_active': True
        }
        updated_asset = asset_manager.update_asset(new_asset['id'], update_data)
        logger.info(f"更新资产成功: {updated_asset['name']}")
        
        # 4. 删除测试资产
        logger.info("4. 删除测试资产")
        if asset_manager.delete_asset(new_asset['id']):
            logger.info("删除资产成功")
        
    except Exception as e:
        logger.error(f"资产操作失败: {e}")


def demo_user_operations(user_manager: UserManager):
    """演示用户操作"""
    logger.info("=== 用户管理演示 ===")
    
    # 1. 获取用户列表
    logger.info("1. 获取用户列表")
    users = user_manager.get_users()
    logger.info(f"当前共有 {len(users)} 个用户")
    
    # 2. 创建测试用户
    logger.info("2. 创建测试用户")
    test_user = {
        'username': 'test_user_001',
        'name': '测试用户001',
        'email': '<EMAIL>',
        'is_active': True,
        'comment': '自动化测试用户'
    }
    
    try:
        new_user = user_manager.create_user(test_user)
        logger.info(f"创建用户成功: {new_user['username']} (ID: {new_user['id']})")
        
        # 3. 更新用户
        logger.info("3. 更新用户信息")
        update_data = {
            'comment': '更新后的测试用户',
            'phone': '13800138000'
        }
        updated_user = user_manager.update_user(new_user['id'], update_data)
        logger.info(f"更新用户成功: {updated_user['username']}")
        
        # 4. 删除测试用户
        logger.info("4. 删除测试用户")
        if user_manager.delete_user(new_user['id']):
            logger.info("删除用户成功")
            
    except Exception as e:
        logger.error(f"用户操作失败: {e}")


def demo_cloud_sync(asset_manager: AssetManager, cloud_config: dict):
    """演示云资产同步"""
    logger.info("=== 云资产同步演示 ===")
    
    # 模拟云平台资产数据
    mock_cloud_assets = [
        {
            'name': 'web-server-001',
            'address': '*************',
            'os_type': 'Linux',
            'status': 'active',
            'tags': ['web', 'nginx']
        },
        {
            'name': 'db-server-001',
            'address': '*************',
            'os_type': 'Linux',
            'status': 'active',
            'tags': ['database', 'mysql']
        },
        {
            'name': 'win-server-001',
            'address': '*************',
            'os_type': 'Windows',
            'status': 'active',
            'tags': ['windows', 'iis']
        }
    ]
    
    # 执行同步
    try:
        # 这里需要实际的节点ID和域ID，示例中使用模拟值
        node_id = "mock-node-id"
        domain_id = "mock-domain-id"
        
        sync_result = asset_manager.sync_assets_from_cloud(
            mock_cloud_assets, node_id, domain_id
        )
        
        logger.info("云资产同步结果:")
        logger.info(f"  总数: {sync_result['total']}")
        logger.info(f"  创建: {sync_result['created']}")
        logger.info(f"  更新: {sync_result['updated']}")
        logger.info(f"  跳过: {sync_result['skipped']}")
        logger.info(f"  失败: {sync_result['failed']}")
        
    except Exception as e:
        logger.error(f"云资产同步失败: {e}")


def demo_batch_operations(asset_manager: AssetManager):
    """演示批量操作"""
    logger.info("=== 批量操作演示 ===")
    
    # 批量创建资产
    batch_assets = [
        {
            'name': f'batch-server-{i:03d}',
            'address': f'192.168.2.{100+i}',
            'platform': 'Linux',
            'protocols': [{'name': 'ssh', 'port': 22}],
            'comment': f'批量创建的测试资产 {i}'
        }
        for i in range(1, 4)  # 创建3个测试资产
    ]
    
    try:
        results = asset_manager.batch_create_assets(batch_assets)
        
        success_count = sum(1 for r in results if r['status'] == 'success')
        logger.info(f"批量创建完成: 成功 {success_count}/{len(batch_assets)}")
        
        # 清理测试资产
        logger.info("清理测试资产...")
        for result in results:
            if result['status'] == 'success':
                asset_id = result['asset']['id']
                asset_manager.delete_asset(asset_id)
                
    except Exception as e:
        logger.error(f"批量操作失败: {e}")


def main():
    """主函数"""
    try:
        # 加载配置
        logger.info("加载配置文件...")
        js_config, cloud_config = load_config()
        
        # 初始化JumpServer客户端
        logger.info("初始化JumpServer客户端...")
        client = JumpServerClient(js_config['jumpserver'])
        
        # 健康检查
        if not client.health_check():
            logger.error("JumpServer连接失败")
            return
            
        logger.info("JumpServer连接成功")
        
        # 初始化管理器
        asset_manager = AssetManager(client)
        user_manager = UserManager(client)
        permission_manager = PermissionManager(client)
        
        # 演示各种操作
        demo_asset_operations(asset_manager)
        demo_user_operations(user_manager)
        demo_cloud_sync(asset_manager, cloud_config)
        demo_batch_operations(asset_manager)
        
        logger.info("所有演示完成")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise


if __name__ == '__main__':
    main()
