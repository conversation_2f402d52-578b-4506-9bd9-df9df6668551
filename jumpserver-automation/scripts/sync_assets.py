#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云资产同步脚本
从多个云池自动同步资产到JumpServer
"""

import sys
import os
import yaml
import logging
import argparse
from pathlib import Path
from datetime import datetime
import json

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from jumpserver_client import JumpServerClient
from asset_manager import AssetManager
from cloud_pool_manager import CloudPoolManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AssetSyncService:
    """资产同步服务"""
    
    def __init__(self, config_dir: Path):
        """
        初始化同步服务
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = config_dir
        self.js_config = None
        self.cloud_config = None
        self.client = None
        self.asset_manager = None
        self.cloud_manager = None
        
        self._load_configs()
        self._init_managers()
        
    def _load_configs(self):
        """加载配置文件"""
        logger.info("加载配置文件...")
        
        # 加载JumpServer配置
        js_config_file = self.config_dir / 'jumpserver_config.yaml'
        with open(js_config_file, 'r', encoding='utf-8') as f:
            self.js_config = yaml.safe_load(f)
            
        # 加载云池配置
        cloud_config_file = self.config_dir / 'cloud_pools.yaml'
        with open(cloud_config_file, 'r', encoding='utf-8') as f:
            self.cloud_config = yaml.safe_load(f)
            
        logger.info("配置文件加载完成")
        
    def _init_managers(self):
        """初始化管理器"""
        logger.info("初始化管理器...")
        
        # 初始化JumpServer客户端
        self.client = JumpServerClient(self.js_config['jumpserver'])
        
        # 健康检查
        if not self.client.health_check():
            raise Exception("JumpServer连接失败")
            
        # 初始化管理器
        self.asset_manager = AssetManager(self.client)
        self.cloud_manager = CloudPoolManager(self.cloud_config)
        
        logger.info("管理器初始化完成")
        
    def sync_single_pool(self, pool_name: str) -> dict:
        """
        同步单个云池
        
        Args:
            pool_name: 云池名称
            
        Returns:
            同步结果
        """
        logger.info(f"开始同步云池: {pool_name}")
        
        try:
            # 获取云池配置
            pool_config = self.cloud_config['cloud_pools'].get(pool_name)
            if not pool_config:
                raise ValueError(f"云池配置不存在: {pool_name}")
                
            # 检查是否启用资产发现
            if not pool_config.get('discovery', {}).get('enabled', False):
                logger.info(f"云池 {pool_name} 未启用资产发现，跳过同步")
                return {'status': 'skipped', 'reason': '未启用资产发现'}
                
            # 获取云资产
            cloud_assets = self.cloud_manager.discover_assets(pool_name)
            if not cloud_assets:
                logger.warning(f"云池 {pool_name} 未发现任何资产")
                return {'status': 'success', 'assets_count': 0}
                
            logger.info(f"从云池 {pool_name} 发现 {len(cloud_assets)} 个资产")
            
            # 获取或创建JumpServer节点和域
            node_id = self._ensure_node_exists(pool_config)
            domain_id = self._ensure_domain_exists(pool_config)
            
            # 执行同步
            sync_result = self.asset_manager.sync_assets_from_cloud(
                cloud_assets, node_id, domain_id
            )
            
            # 添加云池信息到结果
            sync_result['pool_name'] = pool_name
            sync_result['pool_type'] = pool_config['type']
            sync_result['sync_time'] = datetime.now().isoformat()
            
            logger.info(f"云池 {pool_name} 同步完成: "
                       f"创建 {sync_result['created']}, "
                       f"更新 {sync_result['updated']}, "
                       f"跳过 {sync_result['skipped']}, "
                       f"失败 {sync_result['failed']}")
            
            return sync_result
            
        except Exception as e:
            logger.error(f"同步云池 {pool_name} 失败: {e}")
            return {
                'status': 'failed',
                'pool_name': pool_name,
                'error': str(e),
                'sync_time': datetime.now().isoformat()
            }
            
    def sync_all_pools(self) -> dict:
        """
        同步所有云池
        
        Returns:
            总体同步结果
        """
        logger.info("开始同步所有云池")
        
        all_results = {
            'sync_time': datetime.now().isoformat(),
            'total_pools': 0,
            'success_pools': 0,
            'failed_pools': 0,
            'skipped_pools': 0,
            'total_assets': {
                'created': 0,
                'updated': 0,
                'skipped': 0,
                'failed': 0
            },
            'pool_results': []
        }
        
        cloud_pools = self.cloud_config['cloud_pools']
        all_results['total_pools'] = len(cloud_pools)
        
        for pool_name in cloud_pools:
            result = self.sync_single_pool(pool_name)
            all_results['pool_results'].append(result)
            
            # 统计结果
            if result.get('status') == 'success':
                all_results['success_pools'] += 1
                if 'created' in result:
                    all_results['total_assets']['created'] += result['created']
                    all_results['total_assets']['updated'] += result['updated']
                    all_results['total_assets']['skipped'] += result['skipped']
                    all_results['total_assets']['failed'] += result['failed']
            elif result.get('status') == 'skipped':
                all_results['skipped_pools'] += 1
            else:
                all_results['failed_pools'] += 1
                
        logger.info(f"所有云池同步完成: "
                   f"成功 {all_results['success_pools']}, "
                   f"失败 {all_results['failed_pools']}, "
                   f"跳过 {all_results['skipped_pools']}")
        
        return all_results
        
    def _ensure_node_exists(self, pool_config: dict) -> str:
        """
        确保节点存在
        
        Args:
            pool_config: 云池配置
            
        Returns:
            节点ID
        """
        node_name = pool_config['jumpserver']['node_name']
        
        # 查找现有节点
        nodes = self.client.get_paginated('/api/v1/assets/nodes/')
        for node in nodes:
            if node['value'] == node_name:
                logger.info(f"找到现有节点: {node_name}")
                return node['id']
                
        # 创建新节点
        node_data = {
            'value': node_name,
            'comment': f"自动创建的节点: {pool_config['description']}"
        }
        
        new_node = self.client.post('/api/v1/assets/nodes/', node_data)
        logger.info(f"创建新节点: {node_name} (ID: {new_node['id']})")
        return new_node['id']
        
    def _ensure_domain_exists(self, pool_config: dict) -> str:
        """
        确保域存在
        
        Args:
            pool_config: 云池配置
            
        Returns:
            域ID
        """
        domain_name = pool_config['jumpserver']['domain_name']
        
        # 查找现有域
        domains = self.client.get_paginated('/api/v1/assets/domains/')
        for domain in domains:
            if domain['name'] == domain_name:
                logger.info(f"找到现有域: {domain_name}")
                return domain['id']
                
        # 创建新域
        domain_data = {
            'name': domain_name,
            'comment': f"自动创建的域: {pool_config['description']}"
        }
        
        new_domain = self.client.post('/api/v1/assets/domains/', domain_data)
        logger.info(f"创建新域: {domain_name} (ID: {new_domain['id']})")
        return new_domain['id']
        
    def generate_report(self, results: dict, output_file: str = None):
        """
        生成同步报告
        
        Args:
            results: 同步结果
            output_file: 输出文件路径
        """
        report = {
            'summary': {
                '同步时间': results['sync_time'],
                '云池总数': results['total_pools'],
                '成功云池': results['success_pools'],
                '失败云池': results['failed_pools'],
                '跳过云池': results['skipped_pools'],
                '资产统计': results['total_assets']
            },
            'details': results['pool_results']
        }
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            logger.info(f"同步报告已保存到: {output_file}")
        else:
            print(json.dumps(report, ensure_ascii=False, indent=2))


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='JumpServer云资产同步工具')
    parser.add_argument('--config-dir', '-c', 
                       default=Path(__file__).parent.parent / 'config',
                       type=Path, help='配置文件目录')
    parser.add_argument('--pool', '-p', help='指定要同步的云池名称')
    parser.add_argument('--output', '-o', help='报告输出文件路径')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式')
    
    args = parser.parse_args()
    
    try:
        # 初始化同步服务
        sync_service = AssetSyncService(args.config_dir)
        
        if args.dry_run:
            logger.info("试运行模式，不会实际修改数据")
            # 在试运行模式下，可以添加验证逻辑
            return
            
        # 执行同步
        if args.pool:
            # 同步指定云池
            result = sync_service.sync_single_pool(args.pool)
            results = {
                'sync_time': datetime.now().isoformat(),
                'total_pools': 1,
                'success_pools': 1 if result.get('status') == 'success' else 0,
                'failed_pools': 1 if result.get('status') == 'failed' else 0,
                'skipped_pools': 1 if result.get('status') == 'skipped' else 0,
                'total_assets': {
                    'created': result.get('created', 0),
                    'updated': result.get('updated', 0),
                    'skipped': result.get('skipped', 0),
                    'failed': result.get('failed', 0)
                },
                'pool_results': [result]
            }
        else:
            # 同步所有云池
            results = sync_service.sync_all_pools()
            
        # 生成报告
        sync_service.generate_report(results, args.output)
        
    except Exception as e:
        logger.error(f"同步失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
