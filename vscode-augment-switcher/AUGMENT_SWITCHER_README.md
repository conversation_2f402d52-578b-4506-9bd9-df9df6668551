# VSCode Augment 自动账号切换工具

这是一个专为VSCode Augment插件设计的自动账号切换工具，支持邮箱池管理、智能切换策略和自动化运行。

## 🚀 功能特性

### 核心功能
- **邮箱池管理**: 支持多个邮箱账号的统一管理
- **智能切换**: 基于使用频率和时间的智能账号选择
- **自动化运行**: 守护进程自动监控和切换账号
- **使用统计**: 详细的账号使用统计和历史记录
- **安全性**: 本地存储，不上传任何敏感信息

### 高级特性
- **负载均衡**: 自动选择使用频率最低的账号
- **冷却时间**: 避免频繁切换同一账号
- **健康检查**: 定期检查账号状态
- **日志记录**: 完整的操作日志和错误追踪
- **跨平台**: 支持 macOS、Windows、Linux

## 📦 安装

### 快速安装
```bash
# 1. 克隆或下载项目文件
# 2. 运行安装脚本
chmod +x setup_augment_switcher.sh
./setup_augment_switcher.sh
```

### 手动安装
```bash
# 1. 安装Python依赖
pip3 install schedule

# 2. 设置执行权限
chmod +x augment_account_switcher.py
chmod +x auto_switcher_daemon.py

# 3. 创建配置文件
cp email_pool_template.json email_pool.json
```

## ⚙️ 配置

### 1. 编辑邮箱池配置
```bash
nano email_pool.json
```

配置文件示例：
```json
{
  "email_pool": [
    {
      "email": "<EMAIL>",
      "password": "password1",
      "api_key": "",
      "active": true,
      "notes": "主账号"
    },
    {
      "email": "<EMAIL>",
      "password": "password2",
      "api_key": "",
      "active": true,
      "notes": "备用账号"
    }
  ],
  "current_account": null,
  "switch_interval_hours": 24,
  "auto_switch": false
}
```

### 2. 通过命令行添加账号
```bash
./augment_<NAME_EMAIL> password123 --notes "新账号"
```

## 🎯 使用方法

### 基本命令

#### 查看所有账号
```bash
./augment_switch list
```

#### 手动切换账号
```bash
# 自动选择下一个账号
./augment_switch switch --restart

# 切换到指定账号
./augment_switch switch --email <EMAIL> --restart
```

#### 查看当前状态
```bash
./augment_switch status
```

#### 查看切换历史
```bash
./augment_switch history --limit 20
```

#### 自动切换（如果需要）
```bash
./augment_switch auto --hours 24 --restart
```

### 守护进程模式

#### 启动自动切换守护进程
```bash
# 前台运行
./augment_daemon

# 后台运行
nohup ./augment_daemon > daemon.log 2>&1 &
```

#### macOS 自动启动设置
```bash
# 启用开机自动启动
launchctl load ~/Library/LaunchAgents/com.augment.account.switcher.plist

# 停用自动启动
launchctl unload ~/Library/LaunchAgents/com.augment.account.switcher.plist
```

## 🧠 智能切换策略

工具采用以下策略选择下一个账号：

1. **优先级排序**:
   - 从未使用过的账号（最高优先级）
   - 使用次数最少的账号
   - 距离上次使用时间最长的账号

2. **负载均衡**:
   - 避免重复使用同一账号
   - 平均分配使用频率
   - 考虑账号的冷却时间

3. **时间因素**:
   - 每24小时强制切换一次
   - 每天的使用时间会影响选择权重

## 📊 数据存储

### SQLite 数据库
- `account_usage.db`: 存储账号使用统计
- 表结构:
  - `account_usage`: 账号使用记录
  - `switch_history`: 切换历史记录

### 配置文件
- `email_pool.json`: 邮箱池配置
- `augment_switcher.log`: 操作日志

## 🔧 高级配置

### VSCode 设置路径
工具会自动检测并更新以下路径的VSCode设置：
- **macOS**: `~/Library/Application Support/Code/User/settings.json`
- **Windows**: `%APPDATA%/Code/User/settings.json`
- **Linux**: `~/.config/Code/User/settings.json`

### Augment 缓存清理
自动清理以下Augment相关缓存：
- 全局存储目录
- 工作区存储
- 认证令牌

## 🚨 注意事项

### 安全建议
1. **密码安全**: 配置文件包含明文密码，请确保文件权限安全
2. **备份配置**: 定期备份 `email_pool.json` 配置文件
3. **日志清理**: 定期清理日志文件避免占用过多空间

### 使用限制
1. **API限制**: 注意Augment服务的API调用限制
2. **切换频率**: 避免过于频繁的账号切换
3. **网络环境**: 确保网络连接稳定

### 故障排除
1. **权限问题**: 确保脚本有执行权限
2. **Python环境**: 确保Python3和pip3正确安装
3. **VSCode路径**: 检查VSCode安装路径是否正确

## 📝 日志和监控

### 查看日志
```bash
# 主程序日志
tail -f augment_switcher.log

# 守护进程日志
tail -f auto_switcher_daemon.log
```

### 监控命令
```bash
# 检查守护进程状态
ps aux | grep auto_switcher_daemon

# 查看最近的切换记录
./augment_switch history --limit 5
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

MIT License - 详见LICENSE文件
