#!/usr/bin/env python3
"""
VSCode Augment 自动账号切换工具
支持邮箱池管理和自动账号切换
"""

import json
import os
import sys
import time
import random
import subprocess
import sqlite3
from pathlib import Path
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('augment_switcher.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AugmentAccountSwitcher:
    def __init__(self, config_file: str = "email_pool.json"):
        self.config_file = config_file
        self.vscode_settings_path = self._get_vscode_settings_path()
        self.augment_config_path = self._get_augment_config_path()
        self.db_path = "account_usage.db"
        self.email_pool = []
        self.current_account = None
        
        # 初始化数据库
        self._init_database()
        
        # 加载配置
        self._load_config()
    
    def _get_vscode_settings_path(self) -> Path:
        """获取VSCode设置文件路径"""
        if sys.platform == "darwin":  # macOS
            return Path.home() / "Library/Application Support/Code/User/settings.json"
        elif sys.platform == "win32":  # Windows
            return Path.home() / "AppData/Roaming/Code/User/settings.json"
        else:  # Linux
            return Path.home() / ".config/Code/User/settings.json"
    
    def _get_augment_config_path(self) -> Path:
        """获取Augment配置文件路径"""
        if sys.platform == "darwin":  # macOS
            return Path.home() / "Library/Application Support/Code/User/globalStorage/augmentcode.augment"
        elif sys.platform == "win32":  # Windows
            return Path.home() / "AppData/Roaming/Code/User/globalStorage/augmentcode.augment"
        else:  # Linux
            return Path.home() / ".config/Code/User/globalStorage/augmentcode.augment"
    
    def _init_database(self):
        """初始化SQLite数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS account_usage (
                email TEXT PRIMARY KEY,
                last_used TIMESTAMP,
                usage_count INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS switch_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                from_email TEXT,
                to_email TEXT,
                switch_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reason TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def _load_config(self):
        """加载邮箱池配置"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.email_pool = config.get('email_pool', [])
                self.current_account = config.get('current_account')
        else:
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            "email_pool": [
                {
                    "email": "<EMAIL>",
                    "password": "password1",
                    "api_key": "",
                    "active": True,
                    "notes": "主账号"
                },
                {
                    "email": "<EMAIL>", 
                    "password": "password2",
                    "api_key": "",
                    "active": True,
                    "notes": "备用账号1"
                }
            ],
            "current_account": None,
            "switch_interval_hours": 24,
            "auto_switch": False
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"已创建默认配置文件: {self.config_file}")
        logger.info("请编辑配置文件添加你的邮箱账号")
    
    def add_email_account(self, email: str, password: str, api_key: str = "", notes: str = ""):
        """添加邮箱账号到池中"""
        new_account = {
            "email": email,
            "password": password,
            "api_key": api_key,
            "active": True,
            "notes": notes
        }
        
        # 检查是否已存在
        for account in self.email_pool:
            if account['email'] == email:
                logger.warning(f"邮箱 {email} 已存在")
                return False
        
        self.email_pool.append(new_account)
        self._save_config()
        
        # 添加到数据库
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(
            "INSERT OR IGNORE INTO account_usage (email) VALUES (?)",
            (email,)
        )
        conn.commit()
        conn.close()
        
        logger.info(f"已添加邮箱账号: {email}")
        return True
    
    def _save_config(self):
        """保存配置到文件"""
        config = {
            "email_pool": self.email_pool,
            "current_account": self.current_account,
            "switch_interval_hours": 24,
            "auto_switch": False
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    
    def get_next_account(self) -> Optional[Dict]:
        """获取下一个可用账号（基于使用频率和时间）"""
        if not self.email_pool:
            logger.error("邮箱池为空")
            return None
        
        # 获取活跃账号
        active_accounts = [acc for acc in self.email_pool if acc.get('active', True)]
        if not active_accounts:
            logger.error("没有活跃的账号")
            return None
        
        # 从数据库获取使用统计
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        account_stats = {}
        for account in active_accounts:
            cursor.execute(
                "SELECT last_used, usage_count FROM account_usage WHERE email = ?",
                (account['email'],)
            )
            result = cursor.fetchone()
            if result:
                last_used, usage_count = result
                account_stats[account['email']] = {
                    'last_used': last_used,
                    'usage_count': usage_count or 0,
                    'account': account
                }
            else:
                account_stats[account['email']] = {
                    'last_used': None,
                    'usage_count': 0,
                    'account': account
                }
        
        conn.close()
        
        # 选择策略：优先选择最少使用且最久未使用的账号
        best_account = None
        best_score = float('inf')
        
        for email, stats in account_stats.items():
            # 跳过当前账号
            if self.current_account and email == self.current_account.get('email'):
                continue
            
            score = stats['usage_count']
            
            # 如果从未使用过，优先级最高
            if stats['last_used'] is None:
                score = -1
            else:
                # 计算距离上次使用的小时数
                last_used = datetime.fromisoformat(stats['last_used'])
                hours_since_last_use = (datetime.now() - last_used).total_seconds() / 3600
                score = stats['usage_count'] - (hours_since_last_use / 24)  # 每天减少1分
            
            if score < best_score:
                best_score = score
                best_account = stats['account']
        
        return best_account
    
    def switch_account(self, target_account: Optional[Dict] = None, reason: str = "manual") -> bool:
        """切换到指定账号或自动选择下一个账号"""
        if target_account is None:
            target_account = self.get_next_account()
        
        if target_account is None:
            logger.error("无法找到可用的账号")
            return False
        
        old_email = self.current_account.get('email') if self.current_account else None
        new_email = target_account['email']
        
        logger.info(f"正在切换账号: {old_email} -> {new_email}")
        
        # 更新VSCode设置
        if not self._update_vscode_settings(target_account):
            logger.error("更新VSCode设置失败")
            return False
        
        # 清理Augment缓存
        self._clear_augment_cache()
        
        # 更新当前账号
        self.current_account = target_account
        self._save_config()
        
        # 更新数据库
        self._update_usage_stats(new_email)
        self._log_switch_history(old_email, new_email, reason)
        
        logger.info(f"账号切换成功: {new_email}")
        return True

    def _update_vscode_settings(self, account: Dict) -> bool:
        """更新VSCode设置文件"""
        try:
            settings = {}

            # 读取现有设置
            if self.vscode_settings_path.exists():
                with open(self.vscode_settings_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

            # 更新Augment相关设置
            settings.update({
                "augment.email": account['email'],
                "augment.autoLogin": True,
                # 如果有API key，也可以设置
                "augment.apiKey": account.get('api_key', '')
            })

            # 确保目录存在
            self.vscode_settings_path.parent.mkdir(parents=True, exist_ok=True)

            # 写入设置
            with open(self.vscode_settings_path, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            logger.info("VSCode设置已更新")
            return True

        except Exception as e:
            logger.error(f"更新VSCode设置失败: {e}")
            return False

    def _clear_augment_cache(self):
        """清理Augment插件缓存"""
        try:
            # 清理Augment全局存储
            if self.augment_config_path.exists():
                import shutil
                shutil.rmtree(self.augment_config_path, ignore_errors=True)
                logger.info("Augment缓存已清理")

            # 清理VSCode工作区存储中的Augment数据
            workspace_storage_paths = [
                Path.home() / "Library/Application Support/Code/User/workspaceStorage",  # macOS
                Path.home() / "AppData/Roaming/Code/User/workspaceStorage",  # Windows
                Path.home() / ".config/Code/User/workspaceStorage"  # Linux
            ]

            for storage_path in workspace_storage_paths:
                if storage_path.exists():
                    for workspace_dir in storage_path.iterdir():
                        augment_file = workspace_dir / "state.vscdb"
                        if augment_file.exists():
                            try:
                                # 这里可以选择性清理Augment相关的状态
                                pass
                            except:
                                pass

        except Exception as e:
            logger.warning(f"清理缓存时出现警告: {e}")

    def _update_usage_stats(self, email: str):
        """更新账号使用统计"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT OR REPLACE INTO account_usage (email, last_used, usage_count)
            VALUES (?, ?, COALESCE((SELECT usage_count FROM account_usage WHERE email = ?), 0) + 1)
        ''', (email, datetime.now().isoformat(), email))

        conn.commit()
        conn.close()

    def _log_switch_history(self, from_email: Optional[str], to_email: str, reason: str):
        """记录切换历史"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            "INSERT INTO switch_history (from_email, to_email, reason) VALUES (?, ?, ?)",
            (from_email, to_email, reason)
        )

        conn.commit()
        conn.close()

    def list_accounts(self) -> List[Dict]:
        """列出所有账号及其状态"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        accounts_info = []
        for account in self.email_pool:
            email = account['email']
            cursor.execute(
                "SELECT last_used, usage_count FROM account_usage WHERE email = ?",
                (email,)
            )
            result = cursor.fetchone()

            info = {
                'email': email,
                'active': account.get('active', True),
                'notes': account.get('notes', ''),
                'is_current': self.current_account and email == self.current_account.get('email'),
                'last_used': result[0] if result else None,
                'usage_count': result[1] if result else 0
            }
            accounts_info.append(info)

        conn.close()
        return accounts_info

    def get_switch_history(self, limit: int = 10) -> List[Dict]:
        """获取切换历史"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT from_email, to_email, switch_time, reason
            FROM switch_history
            ORDER BY switch_time DESC
            LIMIT ?
        ''', (limit,))

        history = []
        for row in cursor.fetchall():
            history.append({
                'from_email': row[0],
                'to_email': row[1],
                'switch_time': row[2],
                'reason': row[3]
            })

        conn.close()
        return history

    def auto_switch_if_needed(self, force_hours: int = 24) -> bool:
        """如果需要则自动切换账号"""
        if not self.current_account:
            logger.info("当前无账号，执行首次切换")
            return self.switch_account(reason="first_time")

        # 检查当前账号的使用时间
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            "SELECT last_used FROM account_usage WHERE email = ?",
            (self.current_account['email'],)
        )
        result = cursor.fetchone()
        conn.close()

        if result and result[0]:
            last_used = datetime.fromisoformat(result[0])
            hours_since_last_switch = (datetime.now() - last_used).total_seconds() / 3600

            if hours_since_last_switch >= force_hours:
                logger.info(f"账号使用超过{force_hours}小时，执行自动切换")
                return self.switch_account(reason="auto_time_limit")

        return False

    def restart_vscode(self):
        """重启VSCode以应用新设置"""
        try:
            # 尝试优雅关闭VSCode
            if sys.platform == "darwin":  # macOS
                subprocess.run(["osascript", "-e", 'quit app "Visual Studio Code"'],
                             check=False, capture_output=True)
                time.sleep(2)
                subprocess.run(["open", "-a", "Visual Studio Code"],
                             check=False, capture_output=True)
            elif sys.platform == "win32":  # Windows
                subprocess.run(["taskkill", "/f", "/im", "Code.exe"],
                             check=False, capture_output=True)
                time.sleep(2)
                subprocess.run(["code"], check=False, capture_output=True)
            else:  # Linux
                subprocess.run(["pkill", "-f", "code"], check=False, capture_output=True)
                time.sleep(2)
                subprocess.run(["code"], check=False, capture_output=True)

            logger.info("VSCode重启命令已执行")

        except Exception as e:
            logger.warning(f"自动重启VSCode失败: {e}")
            logger.info("请手动重启VSCode以应用新的账号设置")


def main():
    """主函数 - 命令行接口"""
    import argparse

    parser = argparse.ArgumentParser(description="VSCode Augment 自动账号切换工具")
    parser.add_argument("--config", default="email_pool.json", help="配置文件路径")

    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 添加账号命令
    add_parser = subparsers.add_parser("add", help="添加新的邮箱账号")
    add_parser.add_argument("email", help="邮箱地址")
    add_parser.add_argument("password", help="密码")
    add_parser.add_argument("--api-key", default="", help="API密钥")
    add_parser.add_argument("--notes", default="", help="备注")

    # 切换账号命令
    switch_parser = subparsers.add_parser("switch", help="切换账号")
    switch_parser.add_argument("--email", help="指定要切换到的邮箱，不指定则自动选择")
    switch_parser.add_argument("--restart", action="store_true", help="切换后重启VSCode")

    # 列出账号命令
    list_parser = subparsers.add_parser("list", help="列出所有账号")

    # 查看历史命令
    history_parser = subparsers.add_parser("history", help="查看切换历史")
    history_parser.add_argument("--limit", type=int, default=10, help="显示条数")

    # 自动切换命令
    auto_parser = subparsers.add_parser("auto", help="自动切换（如果需要）")
    auto_parser.add_argument("--hours", type=int, default=24, help="强制切换的小时数")
    auto_parser.add_argument("--restart", action="store_true", help="切换后重启VSCode")

    # 状态命令
    status_parser = subparsers.add_parser("status", help="显示当前状态")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # 创建切换器实例
    switcher = AugmentAccountSwitcher(args.config)

    try:
        if args.command == "add":
            success = switcher.add_email_account(
                args.email, args.password, args.api_key, args.notes
            )
            if success:
                print(f"✅ 成功添加账号: {args.email}")
            else:
                print(f"❌ 添加账号失败: {args.email}")

        elif args.command == "switch":
            target_account = None
            if args.email:
                # 查找指定邮箱的账号
                for account in switcher.email_pool:
                    if account['email'] == args.email:
                        target_account = account
                        break
                if not target_account:
                    print(f"❌ 未找到邮箱: {args.email}")
                    return

            success = switcher.switch_account(target_account)
            if success:
                email = target_account['email'] if target_account else switcher.current_account['email']
                print(f"✅ 成功切换到账号: {email}")
                if args.restart:
                    print("🔄 正在重启VSCode...")
                    switcher.restart_vscode()
            else:
                print("❌ 账号切换失败")

        elif args.command == "list":
            accounts = switcher.list_accounts()
            if not accounts:
                print("📭 邮箱池为空")
                return

            print("\n📧 邮箱账号列表:")
            print("-" * 80)
            for account in accounts:
                status = "🟢" if account['active'] else "🔴"
                current = "👤" if account['is_current'] else "  "
                last_used = account['last_used'][:19] if account['last_used'] else "从未使用"

                print(f"{current} {status} {account['email']:<30} "
                      f"使用次数: {account['usage_count']:<3} "
                      f"最后使用: {last_used}")
                if account['notes']:
                    print(f"     备注: {account['notes']}")
            print("-" * 80)

        elif args.command == "history":
            history = switcher.get_switch_history(args.limit)
            if not history:
                print("📝 暂无切换历史")
                return

            print(f"\n📝 最近{len(history)}次切换历史:")
            print("-" * 80)
            for record in history:
                from_email = record['from_email'] or "无"
                switch_time = record['switch_time'][:19]
                print(f"{switch_time} | {from_email:<25} -> {record['to_email']:<25} | {record['reason']}")
            print("-" * 80)

        elif args.command == "auto":
            switched = switcher.auto_switch_if_needed(args.hours)
            if switched:
                print(f"✅ 自动切换完成: {switcher.current_account['email']}")
                if args.restart:
                    print("🔄 正在重启VSCode...")
                    switcher.restart_vscode()
            else:
                print("ℹ️  当前账号无需切换")

        elif args.command == "status":
            if switcher.current_account:
                print(f"👤 当前账号: {switcher.current_account['email']}")

                # 获取使用统计
                import sqlite3
                conn = sqlite3.connect(switcher.db_path)
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT last_used, usage_count FROM account_usage WHERE email = ?",
                    (switcher.current_account['email'],)
                )
                result = cursor.fetchone()
                conn.close()

                if result:
                    last_used = result[0][:19] if result[0] else "从未使用"
                    print(f"📊 使用次数: {result[1]}")
                    print(f"🕐 最后使用: {last_used}")

                print(f"📧 邮箱池大小: {len(switcher.email_pool)}")
                active_count = sum(1 for acc in switcher.email_pool if acc.get('active', True))
                print(f"✅ 活跃账号: {active_count}")
            else:
                print("❌ 当前无活跃账号")

    except KeyboardInterrupt:
        print("\n\n👋 操作已取消")
    except Exception as e:
        logger.error(f"执行命令时出错: {e}")
        print(f"❌ 错误: {e}")


if __name__ == "__main__":
    main()
