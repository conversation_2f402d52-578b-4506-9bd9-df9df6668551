#!/bin/bash

# VSCode Augment 账号切换工具安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=================================================="
echo "    VSCode Augment 账号切换工具安装程序"
echo "=================================================="
echo

# 检查Python版本
log_info "检查Python环境..."
if ! command -v python3 &> /dev/null; then
    log_error "Python3 未安装，请先安装Python3"
    exit 1
fi

python_version=$(python3 --version | cut -d' ' -f2)
log_success "Python版本: $python_version"

# 检查pip
if ! command -v pip3 &> /dev/null; then
    log_error "pip3 未安装，请先安装pip3"
    exit 1
fi

# 安装依赖
log_info "安装Python依赖包..."
pip3 install schedule sqlite3 2>/dev/null || true
log_success "依赖包安装完成"

# 设置执行权限
log_info "设置脚本执行权限..."
chmod +x augment_account_switcher.py
chmod +x auto_switcher_daemon.py
log_success "权限设置完成"

# 创建配置文件
if [ ! -f "email_pool.json" ]; then
    log_info "创建配置文件..."
    cp email_pool_template.json email_pool.json
    log_success "配置文件已创建: email_pool.json"
    log_warning "请编辑 email_pool.json 文件，添加你的邮箱账号信息"
else
    log_info "配置文件已存在，跳过创建"
fi

# 创建启动脚本
log_info "创建便捷启动脚本..."

cat > augment_switch << 'EOF'
#!/bin/bash
# Augment 账号切换便捷脚本
python3 "$(dirname "$0")/augment_account_switcher.py" "$@"
EOF

cat > augment_daemon << 'EOF'
#!/bin/bash
# Augment 自动切换守护进程启动脚本
python3 "$(dirname "$0")/auto_switcher_daemon.py" "$@"
EOF

chmod +x augment_switch
chmod +x augment_daemon

log_success "便捷脚本创建完成"

# 创建launchd配置（macOS）
if [[ "$OSTYPE" == "darwin"* ]]; then
    log_info "创建macOS自动启动配置..."
    
    PLIST_PATH="$HOME/Library/LaunchAgents/com.augment.account.switcher.plist"
    SCRIPT_PATH="$(pwd)/auto_switcher_daemon.py"
    
    cat > "$PLIST_PATH" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.augment.account.switcher</string>
    <key>ProgramArguments</key>
    <array>
        <string>/usr/bin/python3</string>
        <string>$SCRIPT_PATH</string>
    </array>
    <key>WorkingDirectory</key>
    <string>$(pwd)</string>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardOutPath</key>
    <string>$(pwd)/daemon_stdout.log</string>
    <key>StandardErrorPath</key>
    <string>$(pwd)/daemon_stderr.log</string>
</dict>
</plist>
EOF
    
    log_success "macOS自动启动配置已创建"
    log_info "要启用自动启动，请运行: launchctl load $PLIST_PATH"
    log_info "要停用自动启动，请运行: launchctl unload $PLIST_PATH"
fi

echo
echo "=================================================="
log_success "安装完成！"
echo "=================================================="
echo
echo "📋 使用说明:"
echo
echo "1. 编辑配置文件:"
echo "   nano email_pool.json"
echo
echo "2. 添加邮箱账号:"
echo "   ./augment_<NAME_EMAIL> your_password --notes '备注'"
echo
echo "3. 列出所有账号:"
echo "   ./augment_switch list"
echo
echo "4. 手动切换账号:"
echo "   ./augment_switch switch --restart"
echo
echo "5. 查看当前状态:"
echo "   ./augment_switch status"
echo
echo "6. 启动自动切换守护进程:"
echo "   ./augment_daemon"
echo
echo "7. 查看切换历史:"
echo "   ./augment_switch history"
echo
echo "=================================================="
log_warning "重要提示:"
echo "1. 请确保在 email_pool.json 中配置正确的邮箱和密码"
echo "2. 首次使用前建议先手动测试切换功能"
echo "3. 守护进程会在后台自动管理账号切换"
echo "=================================================="
