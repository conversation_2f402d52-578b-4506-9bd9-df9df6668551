#!/usr/bin/env python3
"""
VSCode Augment 账号切换工具演示脚本
展示主要功能的使用方法
"""

import os
import json
import time
from augment_account_switcher import AugmentAccountSwitcher

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("    VSCode Augment 自动账号切换工具 - 演示")
    print("=" * 60)
    print()

def demo_setup():
    """演示设置过程"""
    print("🔧 演示: 初始化设置")
    print("-" * 40)
    
    # 创建演示配置
    demo_config = {
        "email_pool": [
            {
                "email": "<EMAIL>",
                "password": "demo_password_1",
                "api_key": "",
                "active": True,
                "notes": "演示账号1 - 主要开发"
            },
            {
                "email": "<EMAIL>",
                "password": "demo_password_2",
                "api_key": "",
                "active": True,
                "notes": "演示账号2 - 测试环境"
            },
            {
                "email": "<EMAIL>",
                "password": "demo_password_3",
                "api_key": "",
                "active": True,
                "notes": "演示账号3 - 生产环境"
            }
        ],
        "current_account": None,
        "switch_interval_hours": 24,
        "auto_switch": False
    }
    
    config_file = "demo_email_pool.json"
    
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(demo_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 创建演示配置文件: {config_file}")
    print(f"📧 配置了 {len(demo_config['email_pool'])} 个演示邮箱账号")
    print()
    
    return config_file

def demo_basic_operations(switcher):
    """演示基本操作"""
    print("📋 演示: 基本操作")
    print("-" * 40)
    
    # 1. 列出所有账号
    print("1️⃣ 列出所有账号:")
    accounts = switcher.list_accounts()
    for i, account in enumerate(accounts, 1):
        status = "🟢" if account['active'] else "🔴"
        current = "👤" if account['is_current'] else "  "
        print(f"   {current} {status} {account['email']:<25} | {account['notes']}")
    print()
    
    # 2. 添加新账号
    print("2️⃣ 添加新账号:")
    success = switcher.add_email_account(
        "<EMAIL>",
        "demo_password_4",
        "",
        "演示账号4 - 新添加"
    )
    if success:
        print("   ✅ 成功添加新账号: <EMAIL>")
    print()
    
    # 3. 获取下一个推荐账号
    print("3️⃣ 获取下一个推荐账号:")
    next_account = switcher.get_next_account()
    if next_account:
        print(f"   🎯 推荐账号: {next_account['email']}")
        print(f"   📝 备注: {next_account['notes']}")
    print()

def demo_account_switching(switcher):
    """演示账号切换"""
    print("🔄 演示: 账号切换")
    print("-" * 40)
    
    # 模拟VSCode设置更新（避免实际修改）
    original_update_method = switcher._update_vscode_settings
    original_clear_method = switcher._clear_augment_cache
    
    def mock_update_vscode_settings(account):
        print(f"   📝 模拟更新VSCode设置: {account['email']}")
        return True
    
    def mock_clear_augment_cache():
        print("   🧹 模拟清理Augment缓存")
    
    switcher._update_vscode_settings = mock_update_vscode_settings
    switcher._clear_augment_cache = mock_clear_augment_cache
    
    # 执行几次切换
    for i in range(3):
        print(f"{i+1}️⃣ 第{i+1}次切换:")
        
        old_account = switcher.current_account['email'] if switcher.current_account else "无"
        
        success = switcher.switch_account(reason=f"demo_switch_{i+1}")
        
        if success:
            new_account = switcher.current_account['email']
            print(f"   ✅ 切换成功: {old_account} -> {new_account}")
        else:
            print("   ❌ 切换失败")
        
        print()
        time.sleep(1)  # 模拟时间间隔
    
    # 恢复原方法
    switcher._update_vscode_settings = original_update_method
    switcher._clear_augment_cache = original_clear_method

def demo_statistics(switcher):
    """演示统计功能"""
    print("📊 演示: 统计和历史")
    print("-" * 40)
    
    # 1. 显示当前状态
    print("1️⃣ 当前状态:")
    if switcher.current_account:
        print(f"   👤 当前账号: {switcher.current_account['email']}")
        print(f"   📝 备注: {switcher.current_account['notes']}")
    else:
        print("   ❌ 当前无活跃账号")
    print()
    
    # 2. 显示使用统计
    print("2️⃣ 账号使用统计:")
    accounts = switcher.list_accounts()
    for account in accounts:
        usage_count = account['usage_count']
        last_used = account['last_used']
        if last_used:
            last_used = last_used[:19]  # 只显示日期时间部分
        else:
            last_used = "从未使用"
        
        print(f"   📧 {account['email']:<25} | 使用次数: {usage_count:<3} | 最后使用: {last_used}")
    print()
    
    # 3. 显示切换历史
    print("3️⃣ 切换历史:")
    history = switcher.get_switch_history(5)
    if history:
        for record in history:
            from_email = record['from_email'] or "无"
            switch_time = record['switch_time'][:19]
            print(f"   🔄 {switch_time} | {from_email:<20} -> {record['to_email']:<20} | {record['reason']}")
    else:
        print("   📝 暂无切换历史")
    print()

def demo_cleanup(config_file):
    """清理演示文件"""
    print("🧹 清理演示文件")
    print("-" * 40)
    
    files_to_remove = [
        config_file,
        "account_usage.db",
        "augment_switcher.log"
    ]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"   🗑️  删除: {file_path}")
    
    print("   ✅ 清理完成")
    print()

def main():
    """主演示函数"""
    print_banner()
    
    try:
        # 1. 设置演示环境
        config_file = demo_setup()
        
        # 2. 创建切换器实例
        switcher = AugmentAccountSwitcher(config_file)
        
        # 3. 演示基本操作
        demo_basic_operations(switcher)
        
        # 4. 演示账号切换
        demo_account_switching(switcher)
        
        # 5. 演示统计功能
        demo_statistics(switcher)
        
        print("🎉 演示完成!")
        print()
        print("💡 提示:")
        print("   - 这只是一个演示，没有实际修改VSCode设置")
        print("   - 要使用真实功能，请运行: ./setup_augment_switcher.sh")
        print("   - 然后编辑 email_pool.json 添加真实的邮箱账号")
        print()
        
        # 询问是否清理演示文件
        response = input("🗑️  是否清理演示文件? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            demo_cleanup(config_file)
        else:
            print("   📁 演示文件保留，你可以查看:")
            print(f"      - 配置文件: {config_file}")
            print("      - 数据库: account_usage.db")
            print("      - 日志文件: augment_switcher.log")
    
    except KeyboardInterrupt:
        print("\n\n👋 演示被中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")

if __name__ == "__main__":
    main()
