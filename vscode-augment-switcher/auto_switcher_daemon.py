#!/usr/bin/env python3
"""
VSCode Augment 自动切换守护进程
定期检查并自动切换账号
"""

import time
import schedule
import logging
from datetime import datetime
from augment_account_switcher import AugmentAccountSwitcher

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_switcher_daemon.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutoSwitcherDaemon:
    def __init__(self, config_file: str = "email_pool.json"):
        self.switcher = AugmentAccountSwitcher(config_file)
        self.running = False
    
    def check_and_switch(self):
        """检查并执行自动切换"""
        try:
            logger.info("执行定期账号检查...")
            
            # 检查是否需要切换
            switched = self.switcher.auto_switch_if_needed(force_hours=24)
            
            if switched:
                logger.info(f"自动切换完成: {self.switcher.current_account['email']}")
                
                # 可选：重启VSCode
                # self.switcher.restart_vscode()
            else:
                logger.info("当前账号无需切换")
                
        except Exception as e:
            logger.error(f"自动切换过程中出错: {e}")
    
    def health_check(self):
        """健康检查"""
        try:
            accounts = self.switcher.list_accounts()
            active_count = sum(1 for acc in accounts if acc['active'])
            
            logger.info(f"健康检查 - 总账号: {len(accounts)}, 活跃账号: {active_count}")
            
            if active_count == 0:
                logger.warning("警告: 没有活跃的账号!")
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
    
    def start_daemon(self):
        """启动守护进程"""
        logger.info("启动 Augment 账号自动切换守护进程")
        
        # 设置定时任务
        schedule.every(6).hours.do(self.check_and_switch)  # 每6小时检查一次
        schedule.every(1).hours.do(self.health_check)      # 每小时健康检查
        
        self.running = True
        
        # 立即执行一次检查
        self.health_check()
        self.check_and_switch()
        
        try:
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次调度
                
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在停止守护进程...")
            self.stop_daemon()
    
    def stop_daemon(self):
        """停止守护进程"""
        self.running = False
        logger.info("守护进程已停止")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="VSCode Augment 自动切换守护进程")
    parser.add_argument("--config", default="email_pool.json", help="配置文件路径")
    parser.add_argument("--check-interval", type=int, default=6, help="检查间隔(小时)")
    
    args = parser.parse_args()
    
    daemon = AutoSwitcherDaemon(args.config)
    
    try:
        daemon.start_daemon()
    except Exception as e:
        logger.error(f"守护进程启动失败: {e}")


if __name__ == "__main__":
    main()
