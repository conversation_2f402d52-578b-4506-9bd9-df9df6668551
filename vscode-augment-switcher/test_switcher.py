#!/usr/bin/env python3
"""
VSCode Augment 账号切换工具测试脚本
"""

import os
import json
import tempfile
import shutil
from pathlib import Path
from augment_account_switcher import AugmentAccountSwitcher

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始测试基本功能...")
    
    # 创建临时配置文件
    test_config = {
        "email_pool": [
            {
                "email": "<EMAIL>",
                "password": "password1",
                "api_key": "",
                "active": True,
                "notes": "测试账号1"
            },
            {
                "email": "<EMAIL>",
                "password": "password2",
                "api_key": "",
                "active": True,
                "notes": "测试账号2"
            }
        ],
        "current_account": None,
        "switch_interval_hours": 24,
        "auto_switch": False
    }
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        config_file = os.path.join(temp_dir, "test_config.json")
        
        # 写入测试配置
        with open(config_file, 'w') as f:
            json.dump(test_config, f, indent=2)
        
        # 创建切换器实例
        switcher = AugmentAccountSwitcher(config_file)
        
        # 测试1: 列出账号
        print("  ✓ 测试列出账号...")
        accounts = switcher.list_accounts()
        assert len(accounts) == 2, f"期望2个账号，实际{len(accounts)}个"
        print(f"    发现 {len(accounts)} 个账号")
        
        # 测试2: 获取下一个账号
        print("  ✓ 测试获取下一个账号...")
        next_account = switcher.get_next_account()
        assert next_account is not None, "应该能获取到下一个账号"
        print(f"    下一个账号: {next_account['email']}")
        
        # 测试3: 添加新账号
        print("  ✓ 测试添加新账号...")
        success = switcher.add_email_account(
            "<EMAIL>", 
            "password3", 
            "", 
            "测试账号3"
        )
        assert success, "添加账号应该成功"
        
        accounts = switcher.list_accounts()
        assert len(accounts) == 3, f"期望3个账号，实际{len(accounts)}个"
        print(f"    现在有 {len(accounts)} 个账号")
        
        # 测试4: 模拟切换（不实际修改VSCode设置）
        print("  ✓ 测试账号切换逻辑...")
        original_update_method = switcher._update_vscode_settings
        
        def mock_update_vscode_settings(account):
            print(f"    模拟更新VSCode设置: {account['email']}")
            return True
        
        switcher._update_vscode_settings = mock_update_vscode_settings
        
        success = switcher.switch_account()
        assert success, "切换账号应该成功"
        assert switcher.current_account is not None, "当前账号不应该为空"
        print(f"    切换到账号: {switcher.current_account['email']}")
        
        # 恢复原方法
        switcher._update_vscode_settings = original_update_method
        
        # 测试5: 查看切换历史
        print("  ✓ 测试切换历史...")
        history = switcher.get_switch_history(5)
        assert len(history) >= 1, "应该有至少1条切换记录"
        print(f"    切换历史记录: {len(history)} 条")
        
        print("✅ 基本功能测试通过!")
        return True

def test_config_file_creation():
    """测试配置文件创建"""
    print("🧪 测试配置文件创建...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_file = os.path.join(temp_dir, "new_config.json")
        
        # 创建切换器（应该自动创建配置文件）
        switcher = AugmentAccountSwitcher(config_file)
        
        # 检查配置文件是否创建
        assert os.path.exists(config_file), "配置文件应该被创建"
        
        # 检查配置文件内容
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        assert "email_pool" in config, "配置文件应该包含email_pool"
        assert isinstance(config["email_pool"], list), "email_pool应该是列表"
        
        print("✅ 配置文件创建测试通过!")
        return True

def test_database_operations():
    """测试数据库操作"""
    print("🧪 测试数据库操作...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_file = os.path.join(temp_dir, "test_config.json")
        
        # 创建测试配置
        test_config = {
            "email_pool": [
                {
                    "email": "<EMAIL>",
                    "password": "password",
                    "api_key": "",
                    "active": True,
                    "notes": "数据库测试账号"
                }
            ],
            "current_account": None
        }
        
        with open(config_file, 'w') as f:
            json.dump(test_config, f)
        
        # 切换到临时目录（数据库会在当前目录创建）
        original_cwd = os.getcwd()
        os.chdir(temp_dir)
        
        try:
            switcher = AugmentAccountSwitcher(config_file)
            
            # 检查数据库文件是否创建
            db_path = os.path.join(temp_dir, "account_usage.db")
            assert os.path.exists(db_path), "数据库文件应该被创建"
            
            # 测试使用统计更新
            switcher._update_usage_stats("<EMAIL>")
            
            # 测试切换历史记录
            switcher._log_switch_history(None, "<EMAIL>", "test")
            
            # 获取历史记录
            history = switcher.get_switch_history(1)
            assert len(history) == 1, "应该有1条历史记录"
            assert history[0]['to_email'] == "<EMAIL>", "历史记录邮箱应该匹配"
            
            print("✅ 数据库操作测试通过!")
            return True
            
        finally:
            os.chdir(original_cwd)

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行 VSCode Augment 账号切换工具测试套件")
    print("=" * 60)
    
    tests = [
        test_config_file_creation,
        test_database_operations,
        test_basic_functionality,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            print(f"\n📋 运行测试: {test.__name__}")
            if test():
                passed += 1
                print(f"✅ {test.__name__} 通过")
            else:
                failed += 1
                print(f"❌ {test.__name__} 失败")
        except Exception as e:
            failed += 1
            print(f"❌ {test.__name__} 失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过! 工具可以正常使用。")
        return True
    else:
        print("⚠️  有测试失败，请检查问题。")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
